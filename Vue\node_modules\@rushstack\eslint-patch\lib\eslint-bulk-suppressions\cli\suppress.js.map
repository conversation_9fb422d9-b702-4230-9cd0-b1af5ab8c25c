{"version": 3, "file": "suppress.js", "sourceRoot": "", "sources": ["../../../src/eslint-bulk-suppressions/cli/suppress.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAY3D,sCAuEC;AAjFD,mDAAuD;AACvD,2CAA6C;AAC7C,4CAAiE;AAQ1D,KAAK,UAAU,aAAa;IACjC,MAAM,IAAI,GAAa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE7C,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnD,IAAA,8BAAiB,GAAE,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,+DAA+D;IAC/D,MAAM,UAAU,GAAgB,IAAI,CAAC,MAAM,CAKzC,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;QACvB,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YACrB,+CAA+C;QACjD,CAAC;aAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACtE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;YAC3B,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;QACjB,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,2CAA2C,GAAG,EAAE,CAAC,CAAC;QACpE,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CACrC,CAAC;IAEF,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CACb,gGAAgG;YAC9F,oDAAoD,CACvD,CAAC;IACJ,CAAC;IAED,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QACrD,MAAM,IAAI,KAAK,CACb,wGAAwG,CACzG,CAAC;IACJ,CAAC;IAED,4DAA4D;IAC5D,MAAM,eAAe,GAAW,IAAI;SACjC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChD,MAAM,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAE9E,qCAAqC;IACrC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC;QAC1E,MAAM,IAAI,KAAK,CACb,gHAAgH,CACjH,CAAC;IACJ,CAAC;IAED,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,6CAAiC,CAAC,GAAG,GAAG,CAAC;IACvD,CAAC;SAAM,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,6CAAiC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9E,CAAC;IAED,MAAM,IAAA,0BAAc,EAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAEnD,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,yEAAyE,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;IAC3G,CAAC;SAAM,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvC,OAAO,CAAC,GAAG,CACT,yDAAyD,UAAU,CAAC,KAAK,gBAAgB,UAAU,CAAC,KAAK,EAAE,CAC5G,CAAC;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport { printSuppressHelp } from './utils/print-help';\nimport { runEslintAsync } from './runEslint';\nimport { ESLINT_BULK_SUPPRESS_ENV_VAR_NAME } from '../constants';\n\ninterface IParsedArgs {\n  rules: string[];\n  all: boolean;\n  files: string[];\n}\n\nexport async function suppressAsync(): Promise<void> {\n  const args: string[] = process.argv.slice(3);\n\n  if (args.includes('--help') || args.includes('-h')) {\n    printSuppressHelp();\n    process.exit(0);\n  }\n\n  // Use reduce to create an object with all the parsed arguments\n  const parsedArgs: IParsedArgs = args.reduce<{\n    rules: string[];\n    all: boolean;\n    files: string[];\n  }>(\n    (acc, arg, index, arr) => {\n      if (arg === '--rule') {\n        // continue because next arg should be the rule\n      } else if (index > 0 && arr[index - 1] === '--rule' && arr[index + 1]) {\n        acc.rules.push(arg);\n      } else if (arg === '--all') {\n        acc.all = true;\n      } else if (arg.startsWith('--')) {\n        throw new Error(`@rushstack/eslint-bulk: Unknown option: ${arg}`);\n      } else {\n        acc.files.push(arg);\n      }\n      return acc;\n    },\n    { rules: [], all: false, files: [] }\n  );\n\n  if (parsedArgs.files.length === 0) {\n    throw new Error(\n      '@rushstack/eslint-bulk: Files argument is required. Use glob patterns to specify files or use ' +\n        '`.` to suppress all files for the specified rules.'\n    );\n  }\n\n  if (parsedArgs.rules.length === 0 && !parsedArgs.all) {\n    throw new Error(\n      '@rushstack/eslint-bulk: Please specify at least one rule to suppress. Use --all to suppress all rules.'\n    );\n  }\n\n  // Find the index of the last argument that starts with '--'\n  const lastOptionIndex: number = args\n    .map((arg, i) => (arg.startsWith('--') ? i : -1))\n    .reduce((lastIndex, currentIndex) => Math.max(lastIndex, currentIndex), -1);\n\n  // Check if options come before files\n  if (parsedArgs.files.some((file) => args.indexOf(file) < lastOptionIndex)) {\n    throw new Error(\n      '@rushstack/eslint-bulk: Unable to parse command line arguments. All options should come before files argument.'\n    );\n  }\n\n  if (parsedArgs.all) {\n    process.env[ESLINT_BULK_SUPPRESS_ENV_VAR_NAME] = '*';\n  } else if (parsedArgs.rules.length > 0) {\n    process.env[ESLINT_BULK_SUPPRESS_ENV_VAR_NAME] = parsedArgs.rules.join(',');\n  }\n\n  await runEslintAsync(parsedArgs.files, 'suppress');\n\n  if (parsedArgs.all) {\n    console.log(`@rushstack/eslint-bulk: Successfully suppressed all rules for file(s) ${parsedArgs.files}`);\n  } else if (parsedArgs.rules.length > 0) {\n    console.log(\n      `@rushstack/eslint-bulk: Successfully suppressed rules ${parsedArgs.rules} for file(s) ${parsedArgs.files}`\n    );\n  }\n}\n"]}