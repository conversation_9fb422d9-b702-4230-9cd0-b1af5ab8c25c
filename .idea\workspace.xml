<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1b07762e-487f-4428-ba20-dc8f34a60a51" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Vue/test-image-upload.js" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30PwK7FDfUWJKHOBBPh8f2HacLs" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "708",
    "RequestMappingsPanelWidth1": "707",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.FlowerShopApplication.executor": "Run",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/flower",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.45172414",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "D:\\IntelliJ IDEA 2025.1.4.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\flower" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="FlowerShopApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="flower-shop" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.flower.FlowerShopApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1b07762e-487f-4428-ba20-dc8f34a60a51" name="Changes" comment="" />
      <created>1753542449209</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753542449209</updated>
      <workItem from="1753542450385" duration="39000" />
      <workItem from="1753542582629" duration="167000" />
      <workItem from="1753543360602" duration="1400000" />
      <workItem from="1753544785904" duration="73000" />
      <workItem from="1753544989528" duration="118000" />
      <workItem from="1753545120737" duration="97000" />
      <workItem from="1753545231097" duration="4615000" />
      <workItem from="1753550702454" duration="9000" />
      <workItem from="1753550760969" duration="6217000" />
      <workItem from="1753593230337" duration="139000" />
      <workItem from="1753593399786" duration="6332000" />
      <workItem from="1753599842585" duration="3270000" />
      <workItem from="1753603159815" duration="17381000" />
      <workItem from="1753623622510" duration="4461000" />
      <workItem from="1753628208465" duration="17000" />
      <workItem from="1753628303609" duration="32000" />
      <workItem from="1753628503417" duration="16000" />
      <workItem from="1753630086180" duration="10000" />
      <workItem from="1753630160690" duration="25000" />
      <workItem from="1753630213571" duration="201000" />
      <workItem from="1753630709949" duration="66000" />
      <workItem from="1753630818299" duration="229000" />
      <workItem from="1753631074583" duration="800000" />
      <workItem from="1753632227985" duration="138000" />
      <workItem from="1753632384627" duration="535000" />
      <workItem from="1753632955597" duration="332000" />
      <workItem from="1753633598803" duration="147000" />
      <workItem from="1753691875745" duration="184000" />
      <workItem from="1753692090637" duration="468000" />
      <workItem from="1753695157541" duration="3883000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>