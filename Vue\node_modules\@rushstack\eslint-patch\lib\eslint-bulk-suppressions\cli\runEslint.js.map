{"version": 3, "file": "runEslint.js", "sourceRoot": "", "sources": ["../../../src/eslint-bulk-suppressions/cli/runEslint.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3D,wCA4CC;AA9CD,2DAAiE;AAE1D,KAAK,UAAU,cAAc,CAAC,KAAe,EAAE,IAA0B;IAC9E,MAAM,GAAG,GAAW,OAAO,CAAC,GAAG,EAAE,CAAC;IAClC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,IAAA,wCAAuB,EAAC,GAAG,CAAC,CAAC;IACjE,MAAM,EAAE,MAAM,EAAE,GAA0D,OAAO,CAAC,UAAU,CAAC,CAAC;IAE9F,IAAI,MAA+B,CAAC;IACpC,MAAM,YAAY,GAAW,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IACzD,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAClD,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,OAA0D,CAAC;IAC/D,IAAI,CAAC;QACH,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,wDAAa,4BAA4B,GAAC,CAAC;IACpE,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,UAAU,CAAC,CAAC,CAAC;YAChB,MAAM,KAAK,EAAE,CAAC;YACd,MAAM;QACR,CAAC;QAED,KAAK,OAAO,CAAC,CAAC,CAAC;YACb,MAAM,KAAK,EAAE,CAAC;YACd,MAAM;QACR,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAgD,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;QACnG,8DAA8D;QAC9D,MAAM,gBAAgB,GAAW,MAAM,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAc,CAAC,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,GAAG,CACT,wGAAwG;QACtG,yBAAyB,GAAG,EAAE,CACjC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type { ESLint as TEslintLegacy } from 'eslint-8';\nimport type { ESLint as TEslint } from 'eslint-9';\nimport { getEslintPathAndVersion } from './utils/get-eslint-cli';\n\nexport async function runEslintAsync(files: string[], mode: 'suppress' | 'prune'): Promise<void> {\n  const cwd: string = process.cwd();\n  const [eslintPath, eslintVersion] = getEslintPathAndVersion(cwd);\n  const { ESLint }: typeof import('eslint-9') | typeof import('eslint-8') = require(eslintPath);\n\n  let eslint: TEslint | TEslintLegacy;\n  const majorVersion: number = parseInt(eslintVersion, 10);\n  if (majorVersion < 9) {\n    eslint = new ESLint({ cwd, useEslintrc: true });\n  } else {\n    eslint = new ESLint({ cwd });\n  }\n\n  let results: (TEslint.LintResult | TEslintLegacy.LintResult)[];\n  try {\n    results = await eslint.lintFiles(files);\n  } catch (e) {\n    throw new Error(`@rushstack/eslint-bulk execution error: ${e.message}`);\n  }\n\n  const { write, prune } = await import('../bulk-suppressions-patch');\n  switch (mode) {\n    case 'suppress': {\n      await write();\n      break;\n    }\n\n    case 'prune': {\n      await prune();\n      break;\n    }\n  }\n\n  if (results.length > 0) {\n    const stylishFormatter: TEslint.Formatter | TEslintLegacy.Formatter = await eslint.loadFormatter();\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const formattedResults: string = await Promise.resolve(stylishFormatter.format(results as any));\n    console.log(formattedResults);\n  }\n\n  console.log(\n    '@rushstack/eslint-bulk: Successfully pruned unused suppressions in all .eslint-bulk-suppressions.json ' +\n      `files under directory ${cwd}`\n  );\n}\n"]}